<?php
/**
 * Test script to check comment database setup
 */

require_once 'comments/database.php';

try {
    $db = CommentDatabase::getInstance();
    
    echo "<h2>Database Connection Test</h2>";
    echo "<p>✅ Database connection successful!</p>";
    
    // Test creating a test comment for an image
    $testImageSlug = 'image-self-30degrees-png';
    
    echo "<h3>Testing Image Comment Creation</h3>";
    echo "<p>Test image slug: <code>$testImageSlug</code></p>";
    
    // Check if we can get comments for this image (should be empty initially)
    $comments = $db->getComments($testImageSlug);
    $commentCount = $db->getCommentCount($testImageSlug);
    
    echo "<p>Current comment count for test image: <strong>$commentCount</strong></p>";
    
    if (empty($comments)) {
        echo "<p>✅ No existing comments found (as expected for new image)</p>";
    } else {
        echo "<p>📝 Found " . count($comments) . " existing comments:</p>";
        echo "<ul>";
        foreach ($comments as $comment) {
            echo "<li>" . htmlspecialchars($comment['content']) . " (by " . htmlspecialchars($comment['name']) . ")</li>";
        }
        echo "</ul>";
    }
    
    echo "<h3>Database Tables Check</h3>";
    
    // Check if tables exist
    $tables = ['users', 'comments', 'comment_votes'];
    foreach ($tables as $table) {
        $fullTableName = $db->getTableName($table);
        try {
            $stmt = $db->pdo->query("SELECT COUNT(*) FROM $fullTableName");
            $count = $stmt->fetchColumn();
            echo "<p>✅ Table <code>$fullTableName</code> exists with $count records</p>";
        } catch (Exception $e) {
            echo "<p>❌ Table <code>$fullTableName</code> error: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h3>Test Comment Submission</h3>";
    echo "<p>You can now test the comment system on the gallery page or test page.</p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Database Error</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please make sure the database is set up correctly.</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; max-width: 800px; margin: 2rem auto; padding: 1rem; }
code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
h2, h3 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 0.5rem; }
</style>
