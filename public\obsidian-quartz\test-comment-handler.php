<?php
/**
 * Test script to check if comment handler is working
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Comment Handler Test</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/comments.css">
</head>
<body>
<?php

echo "<h1>Comment Handler Test</h1>";

// Test 1: Check if comment handler file exists
$handlerPath = __DIR__ . '/comments/comment-handler.php';
echo "<h2>Test 1: File Existence</h2>";
if (file_exists($handlerPath)) {
    echo "✅ Comment handler file exists<br>";
} else {
    echo "❌ Comment handler file not found at: $handlerPath<br>";
}

// Test 2: Check if we can include the database
echo "<h2>Test 2: Database Connection</h2>";
try {
    require_once __DIR__ . '/comments/database.php';
    $db = CommentDatabase::getInstance();
    echo "✅ Database connection successful<br>";
    
    // Test comment count for a test post
    $testCount = $db->getCommentCount('test-post');
    echo "✅ Comment count query works (test-post has $testCount comments)<br>";
} catch (Exception $e) {
    echo "❌ Database error: " . htmlspecialchars($e->getMessage()) . "<br>";
}

// Test 3: Check authentication system
echo "<h2>Test 3: Authentication System</h2>";
try {
    require_once __DIR__ . '/comments/simple-auth.php';
    $currentUser = SimpleAuth::getCurrentUser();
    if ($currentUser) {
        echo "✅ User is logged in: " . htmlspecialchars($currentUser['name']) . "<br>";
    } else {
        echo "ℹ️ No user currently logged in<br>";
    }
} catch (Exception $e) {
    echo "❌ Authentication error: " . htmlspecialchars($e->getMessage()) . "<br>";
}

// Test 4: Test API endpoint directly
echo "<h2>Test 4: API Endpoint Test</h2>";
$testUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/comments/comment-handler.php?action=user';
echo "Testing URL: <a href='$testUrl' target='_blank'>$testUrl</a><br>";

// Test 5: Check configuration
echo "<h2>Test 5: Configuration</h2>";
try {
    $config = $db->getConfig();
    echo "✅ Configuration loaded successfully<br>";
    echo "Comments enabled: " . ($config['comments']['enable_threading'] ? 'Yes' : 'No') . "<br>";
    echo "Max comment length: " . $config['comments']['max_comment_length'] . "<br>";
} catch (Exception $e) {
    echo "❌ Configuration error: " . htmlspecialchars($e->getMessage()) . "<br>";
}

echo "<h2>JavaScript Test</h2>";
echo "<p>Open browser console and run this test:</p>";
echo "<pre>
fetch('comments/comment-handler.php?action=user')
  .then(response => response.json())
  .then(data => console.log('User API test:', data))
  .catch(error => console.error('Error:', error));
</pre>";

echo "<h2>Comment System Test</h2>";
echo "<p>Test the comment system below:</p>";

// Include the comment system
require_once __DIR__ . '/comments/comments-display.php';
echo renderCommentsSection('test-page');

?>

<script src="js/comments.js"></script>
</body>
</html>
