<?php
/**
 * Check PHP error logs
 */

echo "<h1>PHP Error Logs</h1>";

// Get the error log location
$errorLogPath = ini_get('error_log');
echo "<p>Error log path: " . htmlspecialchars($errorLogPath) . "</p>";

// Try common XAMPP log locations
$possibleLogs = [
    $errorLogPath,
    'C:\xampp2\apache\logs\error.log',
    'C:\xampp2\php\logs\php_error_log',
    '/var/log/apache2/error.log',
    '/var/log/php_errors.log'
];

foreach ($possibleLogs as $logPath) {
    if ($logPath && file_exists($logPath)) {
        echo "<h2>Log file: " . htmlspecialchars($logPath) . "</h2>";
        
        // Get the last 50 lines
        $lines = file($logPath);
        if ($lines) {
            $lastLines = array_slice($lines, -50);
            echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 400px; overflow-y: scroll;'>";
            foreach ($lastLines as $line) {
                echo htmlspecialchars($line);
            }
            echo "</pre>";
        } else {
            echo "<p>Could not read log file.</p>";
        }
        break;
    }
}

// Also check if we can write to the error log
error_log("Test log entry from check-logs.php at " . date('Y-m-d H:i:s'));
echo "<p>Test log entry written. Check the logs above for the test message.</p>";

// Show current PHP settings
echo "<h2>PHP Settings</h2>";
echo "<p>Log errors: " . (ini_get('log_errors') ? 'Yes' : 'No') . "</p>";
echo "<p>Display errors: " . (ini_get('display_errors') ? 'Yes' : 'No') . "</p>";
echo "<p>Error reporting level: " . error_reporting() . "</p>";

?>
