/* Comments System Styles */
.comments-section {
    max-width: 800px;
    margin: 2rem auto;
    padding: 1rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.comments-header h3 {
    margin-bottom: 1.5rem;
    color: #333;
    border-bottom: 2px solid #e1e5e9;
    padding-bottom: 0.5rem;
}

/* Comment Form */
.comment-form-container {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background: #e9ecef;
    border-radius: 4px;
}

.user-name {
    font-weight: 600;
    color: #495057;
}

.user-email {
    color: #6c757d;
    font-size: 0.9rem;
}

.btn-link {
    background: none !important;
    border: none !important;
    color: #007bff !important;
    text-decoration: underline;
    cursor: pointer;
    font-size: 0.9rem;
    padding: 0.25rem 0.5rem !important;
}

.btn-link:hover {
    color: #0056b3 !important;
    background: none !important;
}

.user-fields {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;
    color: #333;
    background-color: #fff;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-help {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #6c757d;
}

.character-count {
    text-align: right;
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.form-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-primary:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

/* Comments List */
.comments-container {
    margin-top: 2rem;
}

.loading {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.comment {
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    background: white;
}

.comment-reply {
    margin-left: 2rem;
    margin-top: 1rem;
    border-left: 3px solid #e1e5e9;
    padding-left: 1rem;
}

.comment-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.comment-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.1rem;
}

.comment-meta {
    flex: 1;
}

.comment-author {
    font-weight: 600;
    color: #495057;
}

.comment-date {
    color: #6c757d;
    font-size: 0.875rem;
    margin-left: 0.5rem;
}

.comment-content {
    margin-bottom: 0.75rem;
    line-height: 1.5;
    color: #495057;
}

.comment-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.vote-btn {
    background: none;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.15s ease-in-out;
}

.vote-btn:hover {
    background: #f8f9fa;
}

.vote-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.vote-display {
    font-size: 0.875rem;
    color: #6c757d;
    margin-right: 0.5rem;
}

.reply-btn {
    background: none;
    border: none;
    color: #007bff;
    cursor: pointer;
    font-size: 0.875rem;
    text-decoration: underline;
}

.reply-btn:hover {
    color: #0056b3;
}

#load-more-container {
    text-align: center;
    margin-top: 2rem;
}

/* Messages */
.message {
    padding: 0.75rem 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.message-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.message-error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Responsive */
@media (max-width: 768px) {
    .comments-section {
        margin: 1rem;
        padding: 0.5rem;
    }
    
    .user-fields {
        grid-template-columns: 1fr;
    }
    
    .comment-reply {
        margin-left: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
}

/* No Comments Message */
.no-comments {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
    font-style: italic;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    margin: 1rem 0;
}
