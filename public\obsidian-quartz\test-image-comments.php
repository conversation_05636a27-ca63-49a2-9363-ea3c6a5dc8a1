<?php
/**
 * Test script for image comments functionality
 */

// Load configuration
$config = require_once 'config.php';

// Initialize path constants
require_once 'path-helper.php';
$paths = initPaths($config, __FILE__);

// Set page variables
$page_title = 'Test Image Comments';
$meta_description = 'Testing image comments functionality';

// Test image data
$test_image = [
    'filename' => 'self/30degrees.png',
    'alt' => 'Test image for comments',
    'caption' => 'Test image for comments',
    'category' => 'Test',
    'commentary' => 'This is a test image to verify comment functionality.'
];

// Generate content
ob_start();
?>

<div class="test-container">
    <h1>Test Image Comments</h1>
    <p>Click on the image below to test the comment system:</p>
    
    <div class="test-image-container" style="max-width: 400px; margin: 2rem auto;">
        <img src="<?php echo $paths['images_path'] . htmlspecialchars($test_image['filename']); ?>"
             alt="<?php echo htmlspecialchars($test_image['alt']); ?>"
             data-caption="<?php echo htmlspecialchars($test_image['caption']); ?>"
             data-category="<?php echo htmlspecialchars($test_image['category']); ?>"
             data-commentary="<?php echo htmlspecialchars($test_image['commentary']); ?>"
             style="width: 100%; cursor: pointer; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);"
             onclick="openTestModal(this)">
    </div>
    
    <div class="test-info">
        <h3>Expected Image Slug:</h3>
        <code id="expected-slug"></code>

        <h3>Test Instructions:</h3>
        <ol>
            <li>Click on the image above</li>
            <li>Verify the modal opens with the improved layout (side-by-side on desktop)</li>
            <li>Check that the background uses dark theme colors</li>
            <li>Try posting a comment (check browser console for debug info)</li>
            <li>Verify the comment appears in the comment section</li>
        </ol>

        <h3>Layout Improvements:</h3>
        <ul>
            <li>✅ Dark theme colors (no more white background)</li>
            <li>✅ Side-by-side layout on desktop (image info + comments)</li>
            <li>✅ Stacked layout on mobile</li>
            <li>✅ Better navigation buttons</li>
            <li>✅ Scrollable comment section</li>
        </ul>
    </div>
</div>

<!-- Modal for testing -->
<div id="test-modal" class="gallery-modal">
    <div class="modal-content">
        <span class="modal-close" onclick="closeTestModal()">&times;</span>
        <div class="modal-image-container">
            <img id="test-modal-image" src="" alt="">
        </div>
        <div class="modal-info">
            <h3 id="test-modal-caption"></h3>
            <span id="test-modal-category"></span>
            <div id="test-modal-commentary" class="modal-commentary"></div>
        </div>
        
        <!-- Comments Section for Images -->
        <div id="test-modal-comments-section" class="modal-comments-section">
            <div class="comments-header">
                <h4>Comments (<span id="test-modal-comment-count">0</span>)</h4>
            </div>

            <div class="comment-form-container">
                <div id="test-modal-user-info" class="user-info" style="display: none;">
                    <span class="user-name" id="test-modal-user-name"></span>
                    <span class="user-email" id="test-modal-user-email"></span>
                    <button type="button" id="test-modal-change-user" class="btn btn-link">Change</button>
                </div>

                <form id="test-modal-comment-form" class="comment-form">
                    <input type="hidden" name="post_slug" id="test-modal-post-slug" value="">
                    <input type="hidden" name="parent_id" value="">

                    <div id="test-modal-user-fields" class="user-fields">
                        <div class="form-group">
                            <label for="test-modal-comment-name">Name *</label>
                            <input type="text"
                                   id="test-modal-comment-name"
                                   name="name"
                                   placeholder="Your name"
                                   maxlength="100"
                                   required>
                        </div>

                        <div class="form-group">
                            <label for="test-modal-comment-email">Email *</label>
                            <input type="email"
                                   id="test-modal-comment-email"
                                   name="email"
                                   placeholder="<EMAIL>"
                                   maxlength="255"
                                   required>
                            <small class="form-help">Your email will not be displayed publicly</small>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="test-modal-comment-content">Comment *</label>
                        <textarea id="test-modal-comment-content"
                                  name="content"
                                  placeholder="Share your thoughts about this image..."
                                  rows="3"
                                  maxlength="2000"
                                  required></textarea>
                        <div class="character-count">
                            <span id="test-modal-char-count">0</span> / 2000
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="test-modal-cancel-reply" class="btn btn-secondary" style="display: none;">Cancel Reply</button>
                        <button type="submit" class="btn btn-primary">Post Comment</button>
                    </div>
                </form>
            </div>

            <div id="test-modal-comments-container" class="comments-container">
                <div id="test-modal-comments-loading" class="loading" style="display: none;">
                    Loading comments...
                </div>
                <div id="test-modal-comments-list" class="comments-list">
                    <!-- Comments will be loaded here via JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let testCommentsSystem = null;

function openTestModal(img) {
    const modal = document.getElementById('test-modal');
    const modalImage = document.getElementById('test-modal-image');
    const modalCaption = document.getElementById('test-modal-caption');
    const modalCategory = document.getElementById('test-modal-category');
    const modalCommentary = document.getElementById('test-modal-commentary');
    
    modalImage.src = img.src;
    modalImage.alt = img.alt;
    modalCaption.textContent = img.dataset.caption;
    modalCategory.textContent = img.dataset.category;
    modalCommentary.textContent = img.dataset.commentary;
    
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';
    
    // Initialize comments
    initializeTestComments(img);
}

function closeTestModal() {
    const modal = document.getElementById('test-modal');
    modal.classList.remove('show');
    document.body.style.overflow = '';
    
    if (testCommentsSystem) {
        testCommentsSystem.cleanup();
        testCommentsSystem = null;
    }
}

function initializeTestComments(img) {
    // Create image slug
    const imageSlug = getTestImageSlug(img);
    document.getElementById('expected-slug').textContent = imageSlug;
    
    // Update form
    document.getElementById('test-modal-post-slug').value = imageSlug;
    
    // Create comment system
    testCommentsSystem = new ImageCommentsSystem(imageSlug, {
        form: document.getElementById('test-modal-comment-form'),
        commentsList: document.getElementById('test-modal-comments-list'),
        commentCount: document.getElementById('test-modal-comment-count'),
        loadingIndicator: document.getElementById('test-modal-comments-loading')
    });
    
    testCommentsSystem.init();
}

function getTestImageSlug(img) {
    const src = img.src;
    const pathParts = src.split('/');
    const imgIndex = pathParts.findIndex(part => part === 'img');
    if (imgIndex !== -1 && imgIndex < pathParts.length - 1) {
        const relativePath = pathParts.slice(imgIndex + 1).join('/');
        return 'image-' + relativePath.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
    }
    const filename = src.split('/').pop();
    return 'image-' + filename.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
}

// Close modal on escape key
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        closeTestModal();
    }
});
</script>

<?php
$content = ob_get_clean();

// Include the page template
include 'page template.htm';
?>
