<?php
/**
 * Debug comment submission
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Comment Submission</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/comments.css">
</head>
<body>

<h1>Debug Comment Submission</h1>

<h2>Test Direct API Call</h2>
<button onclick="testUserAPI()">Test User API</button>
<button onclick="testCommentSubmission()">Test Comment Submission</button>
<div id="api-results"></div>

<h2>Manual Comment Form</h2>
<form id="manual-form">
    <div>
        <label>Name:</label>
        <input type="text" id="manual-name" value="Test User" required>
    </div>
    <div>
        <label>Email:</label>
        <input type="email" id="manual-email" value="<EMAIL>" required>
    </div>
    <div>
        <label>Comment:</label>
        <textarea id="manual-content" required>This is a test comment to debug the submission process.</textarea>
    </div>
    <button type="button" onclick="submitManualComment()">Submit Manual Comment</button>
</form>

<div id="manual-results"></div>

<script>
async function testUserAPI() {
    const resultsDiv = document.getElementById('api-results');
    resultsDiv.innerHTML = 'Testing user API...';
    
    try {
        const response = await fetch('comments/comment-handler.php?action=user');
        const result = await response.json();
        resultsDiv.innerHTML = '<h3>User API Result:</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';
    } catch (error) {
        resultsDiv.innerHTML = '<h3>User API Error:</h3><pre>' + error.message + '</pre>';
    }
}

async function testCommentSubmission() {
    const resultsDiv = document.getElementById('api-results');
    resultsDiv.innerHTML = 'Testing comment submission...';
    
    const testData = {
        post_slug: 'debug-test',
        content: 'This is a debug test comment',
        name: 'Debug User',
        email: '<EMAIL>',
        parent_id: null,
        website: '' // honeypot field
    };
    
    try {
        const response = await fetch('comments/comment-handler.php?action=comment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testData)
        });
        
        const result = await response.json();
        resultsDiv.innerHTML += '<h3>Comment Submission Result:</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';
        
        // Also show the raw response
        const responseText = await response.text();
        resultsDiv.innerHTML += '<h3>Raw Response:</h3><pre>' + responseText + '</pre>';
        
    } catch (error) {
        resultsDiv.innerHTML += '<h3>Comment Submission Error:</h3><pre>' + error.message + '</pre>';
    }
}

async function submitManualComment() {
    const resultsDiv = document.getElementById('manual-results');
    resultsDiv.innerHTML = 'Submitting manual comment...';
    
    const name = document.getElementById('manual-name').value;
    const email = document.getElementById('manual-email').value;
    const content = document.getElementById('manual-content').value;
    
    const testData = {
        post_slug: 'manual-test',
        content: content,
        name: name,
        email: email,
        parent_id: null,
        website: '' // honeypot field
    };
    
    try {
        const response = await fetch('comments/comment-handler.php?action=comment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testData)
        });
        
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        
        const result = await response.json();
        resultsDiv.innerHTML = '<h3>Manual Comment Result:</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';
        
    } catch (error) {
        console.error('Error details:', error);
        resultsDiv.innerHTML = '<h3>Manual Comment Error:</h3><pre>' + error.message + '</pre>';
    }
}
</script>

</body>
</html>
