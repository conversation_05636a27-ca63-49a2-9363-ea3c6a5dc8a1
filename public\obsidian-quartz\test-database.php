<?php
/**
 * Test database connection and tables
 */

echo "<h1>Database Connection Test</h1>";

// Test basic PDO connection
try {
    $pdo = new PDO(
        'mysql:host=localhost;dbname=aachipsc;charset=utf8mb4',
        'root',
        '',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    echo "✅ Database connection successful<br>";
    
    // Check if tables exist
    $tables = [
        'aachipsc_blog_users',
        'aachipsc_blog_comments',
        'aachipsc_blog_comment_votes',
        'aachipsc_blog_rate_limits'
    ];
    
    echo "<h2>Table Check</h2>";
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->fetch()) {
            echo "✅ Table '$table' exists<br>";
            
            // Show table structure
            $stmt = $pdo->prepare("DESCRIBE $table");
            $stmt->execute();
            $columns = $stmt->fetchAll();
            echo "<details><summary>Show columns</summary><ul>";
            foreach ($columns as $column) {
                echo "<li>{$column['Field']} ({$column['Type']})</li>";
            }
            echo "</ul></details>";
        } else {
            echo "❌ Table '$table' does not exist<br>";
        }
    }
    
    // Test comment count
    echo "<h2>Comment Count Test</h2>";
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM aachipsc_blog_comments");
    $stmt->execute();
    $result = $stmt->fetch();
    echo "Total comments in database: " . $result['count'] . "<br>";

    // Show recent comments
    echo "<h2>Recent Comments</h2>";
    $stmt = $pdo->prepare("SELECT c.*, u.name, u.email FROM aachipsc_blog_comments c LEFT JOIN aachipsc_blog_users u ON c.user_id = u.id ORDER BY c.created_at DESC LIMIT 5");
    $stmt->execute();
    $comments = $stmt->fetchAll();

    if (empty($comments)) {
        echo "No comments found in database.<br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Post Slug</th><th>User</th><th>Content</th><th>Created</th><th>Approved</th></tr>";
        foreach ($comments as $comment) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($comment['id']) . "</td>";
            echo "<td>" . htmlspecialchars($comment['post_slug']) . "</td>";
            echo "<td>" . htmlspecialchars($comment['name'] ?? 'Unknown') . "</td>";
            echo "<td>" . htmlspecialchars(substr($comment['content'], 0, 50)) . "...</td>";
            echo "<td>" . htmlspecialchars($comment['created_at']) . "</td>";
            echo "<td>" . ($comment['is_approved'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

} catch (PDOException $e) {
    echo "❌ Database connection failed: " . htmlspecialchars($e->getMessage()) . "<br>";
}
?>
