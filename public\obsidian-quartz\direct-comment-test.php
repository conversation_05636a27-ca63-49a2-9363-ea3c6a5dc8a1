<?php
/**
 * Direct comment handler test
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>Direct Comment Handler Test</h1>";

// Test 1: Direct inclusion and method call
echo "<h2>Test 1: Direct Method Call</h2>";

try {
    require_once __DIR__ . '/comments/comment-handler.php';
    
    // Simulate POST data for comment creation
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_GET['action'] = 'comment';
    
    // Create test input
    $testInput = [
        'post_slug' => 'direct-test',
        'content' => 'This is a direct test comment',
        'name' => 'Direct Test User',
        'email' => '<EMAIL>',
        'parent_id' => null,
        'website' => ''
    ];
    
    // Simulate the input stream
    $inputStream = json_encode($testInput);
    
    echo "<p>Test input: <pre>" . htmlspecialchars($inputStream) . "</pre></p>";
    
    // We can't easily simulate php://input, so let's test the database directly
    echo "<h3>Testing Database Operations Directly</h3>";
    
    require_once __DIR__ . '/comments/database.php';
    require_once __DIR__ . '/comments/simple-auth.php';
    
    $db = CommentDatabase::getInstance();
    
    // Test user creation
    $auth = new SimpleAuth();
    $user = $auth->createOrGetUser('Direct Test User', '<EMAIL>');
    echo "<p>✅ User created/retrieved: " . htmlspecialchars($user['name']) . " (ID: {$user['id']})</p>";
    
    // Test comment creation
    $commentId = $db->createComment(
        'direct-test',
        $user['id'],
        'This is a direct test comment',
        null,
        '127.0.0.1',
        'Direct Test User Agent'
    );
    
    echo "<p>✅ Comment created with ID: $commentId</p>";
    
    // Test comment retrieval
    $comments = $db->getComments('direct-test');
    echo "<p>✅ Retrieved " . count($comments) . " comments for 'direct-test'</p>";
    
    if (!empty($comments)) {
        echo "<h4>Retrieved Comments:</h4>";
        foreach ($comments as $comment) {
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>";
            echo "<strong>" . htmlspecialchars($comment['name']) . "</strong><br>";
            echo htmlspecialchars($comment['content']) . "<br>";
            echo "<small>" . htmlspecialchars($comment['created_at']) . "</small>";
            echo "</div>";
        }
    }
    
    // Test comment count
    $count = $db->getCommentCount('direct-test');
    echo "<p>✅ Comment count for 'direct-test': $count</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Stack trace: <pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre></p>";
}

// Test 2: Check if the comment handler endpoint is accessible
echo "<h2>Test 2: HTTP Endpoint Test</h2>";

$testUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/comments/comment-handler.php?action=user';
echo "<p>Testing URL: <a href='$testUrl' target='_blank'>$testUrl</a></p>";

// Use cURL to test the endpoint
if (function_exists('curl_init')) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $testUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<p>HTTP Response Code: $httpCode</p>";
    if ($error) {
        echo "<p>cURL Error: " . htmlspecialchars($error) . "</p>";
    } else {
        echo "<p>Response: <pre>" . htmlspecialchars($response) . "</pre></p>";
    }
} else {
    echo "<p>cURL not available for testing.</p>";
}

?>
