<?php
// Load configuration
$config = require_once 'config.php';

// Initialize path constants
require_once 'path-helper.php';
$paths = initPaths($config, __FILE__);

// Set page variables
$page_title = 'Gallery';
$meta_description = 'Visual gallery showcasing personal photos, memes, educational content, and artwork by A. A. Chips';
$meta_keywords = 'gallery, photos, art, memes, education, A. A. Chips';

// Load gallery data
$gallery_data = [];
$gallery_file = $paths['data_path'] . 'gallery.json';
if (file_exists($gallery_file)) {
    $gallery_data = json_decode(file_get_contents($gallery_file), true);
}

// Generate content
ob_start();
?>

<div class="gallery-container">
    <h1>Gallery</h1>
    <p class="gallery-intro">Explore my visual collection organized into different categories. Click on any image to view it in full size.</p>
    
    <!-- Category Filter Buttons -->
    <div class="gallery-filters">
        <button class="filter-btn active" data-category="all">All</button>
        <?php foreach (array_keys($gallery_data) as $category): ?>
            <button class="filter-btn" data-category="<?php echo strtolower(str_replace(' ', '-', $category)); ?>">
                <?php echo htmlspecialchars($category); ?>
            </button>
        <?php endforeach; ?>
    </div>

    <!-- Gallery Grid -->
    <div class="gallery-grid" id="gallery-grid">
        <?php foreach ($gallery_data as $category => $images): ?>
            <?php foreach ($images as $image): ?>
                <div class="gallery-item" data-category="<?php echo strtolower(str_replace(' ', '-', $category)); ?>">
                    <div class="gallery-item-inner">
                        <img src="<?php echo $paths['images_path'] . htmlspecialchars($image['filename']); ?>"
                             alt="<?php echo htmlspecialchars($image['alt']); ?>"
                             data-caption="<?php echo htmlspecialchars($image['caption']); ?>"
                             data-category="<?php echo htmlspecialchars($category); ?>"
                             data-commentary="<?php echo htmlspecialchars($image['commentary'] ?? ''); ?>"
                             loading="lazy">
                        <div class="gallery-overlay">
                            <div class="gallery-info">
                                <h3><?php echo htmlspecialchars($image['caption']); ?></h3>
                                <span class="gallery-category"><?php echo htmlspecialchars($category); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endforeach; ?>
    </div>
</div>

<!-- Modal for enlarged images -->
<div id="gallery-modal" class="gallery-modal">
    <div class="modal-content">
        <span class="modal-close">&times;</span>
        <div class="modal-image-container">
            <img id="modal-image" src="" alt="">
        </div>
        <div class="modal-info">
            <h3 id="modal-caption"></h3>
            <span id="modal-category"></span>
            <div id="modal-commentary" class="modal-commentary"></div>
        </div>
        <div class="modal-nav">
            <button id="modal-prev" class="modal-nav-btn">&lt;</button>
            <button id="modal-next" class="modal-nav-btn">&gt;</button>
        </div>

        <!-- Comments Section for Images -->
        <div id="modal-comments-section" class="modal-comments-section">
            <div class="comments-header">
                <h4>💬 Comments (<span id="modal-comment-count">0</span>)</h4>
                <small class="comments-hint">Scroll to see all comments</small>
            </div>

            <div class="comment-form-container">
                <div id="modal-user-info" class="user-info" style="display: none;">
                    <span class="user-name" id="modal-user-name"></span>
                    <span class="user-email" id="modal-user-email"></span>
                    <button type="button" id="modal-change-user" class="btn btn-link">Change</button>
                </div>

                <form id="modal-comment-form" class="comment-form">
                    <input type="hidden" name="post_slug" id="modal-post-slug" value="">
                    <input type="hidden" name="parent_id" value="">

                    <div id="modal-user-fields" class="user-fields">
                        <div class="form-group">
                            <label for="modal-comment-name">Name *</label>
                            <input type="text"
                                   id="modal-comment-name"
                                   name="name"
                                   placeholder="Your name"
                                   maxlength="100"
                                   required>
                        </div>

                        <div class="form-group">
                            <label for="modal-comment-email">Email *</label>
                            <input type="email"
                                   id="modal-comment-email"
                                   name="email"
                                   placeholder="<EMAIL>"
                                   maxlength="255"
                                   required>
                            <small class="form-help">Your email will not be displayed publicly</small>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="modal-comment-content">Comment *</label>
                        <textarea id="modal-comment-content"
                                  name="content"
                                  placeholder="Share your thoughts about this image..."
                                  rows="3"
                                  maxlength="2000"
                                  required></textarea>
                        <div class="character-count">
                            <span id="modal-char-count">0</span> / 2000
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="modal-cancel-reply" class="btn btn-secondary" style="display: none;">Cancel Reply</button>
                        <button type="submit" class="btn btn-primary">Post Comment</button>
                    </div>
                </form>
            </div>

            <div id="modal-comments-container" class="comments-container">
                <div id="modal-comments-loading" class="loading" style="display: none;">
                    Loading comments...
                </div>
                <div id="modal-comments-list" class="comments-list">
                    <!-- Comments will be loaded here via JavaScript -->
                </div>
                <div id="modal-load-more-container" style="display: none;">
                    <button id="modal-load-more-comments" class="btn btn-secondary">Load More Comments</button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

// Include the page template
include 'page template.htm';
?>

