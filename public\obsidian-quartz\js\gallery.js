
/**
 * Gallery functionality for masonry grid and modal display
 */

class Gallery {
    constructor() {
        this.modal = document.getElementById('gallery-modal');
        this.modalImage = document.getElementById('modal-image');
        this.modalCaption = document.getElementById('modal-caption');
        this.modalCategory = document.getElementById('modal-category');
        this.modalCommentary = document.getElementById('modal-commentary');
        this.modalClose = document.querySelector('.modal-close');
        this.modalPrev = document.getElementById('modal-prev');
        this.modalNext = document.getElementById('modal-next');
        this.galleryGrid = document.getElementById('gallery-grid');
        this.filterButtons = document.querySelectorAll('.filter-btn');

        // Comment system elements
        this.modalCommentsSection = document.getElementById('modal-comments-section');
        this.modalCommentForm = document.getElementById('modal-comment-form');
        this.modalCommentsList = document.getElementById('modal-comments-list');
        this.modalCommentCount = document.getElementById('modal-comment-count');
        this.modalPostSlug = document.getElementById('modal-post-slug');
        this.modalCommentsLoading = document.getElementById('modal-comments-loading');

        this.currentImages = [];
        this.currentIndex = 0;
        this.currentImageComments = null; // Will hold the CommentsSystem instance for current image

        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupFilters();
        this.updateCurrentImages();
        this.setupCommentSystem();
    }
    
    setupEventListeners() {
        // Gallery item clicks
        this.galleryGrid.addEventListener('click', (e) => {
            const galleryItem = e.target.closest('.gallery-item');
            if (galleryItem) {
                const img = galleryItem.querySelector('img');
                this.openModal(img);
            }
        });
        
        // Modal close events
        this.modalClose.addEventListener('click', () => this.closeModal());
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.closeModal();
            }
        });
        
        // Modal navigation
        this.modalPrev.addEventListener('click', () => this.previousImage());
        this.modalNext.addEventListener('click', () => this.nextImage());
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (this.modal.classList.contains('show')) {
                switch(e.key) {
                    case 'Escape':
                        this.closeModal();
                        break;
                    case 'ArrowLeft':
                        this.previousImage();
                        break;
                    case 'ArrowRight':
                        this.nextImage();
                        break;
                }
            }
        });
    }
    
    setupFilters() {
        this.filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons
                this.filterButtons.forEach(btn => btn.classList.remove('active'));
                // Add active class to clicked button
                button.classList.add('active');
                
                const category = button.dataset.category;
                this.filterImages(category);
            });
        });
    }
    
    filterImages(category) {
        const galleryItems = document.querySelectorAll('.gallery-item');
        
        galleryItems.forEach(item => {
            if (category === 'all' || item.dataset.category === category) {
                item.style.display = 'block';
                // Add animation
                item.style.opacity = '0';
                item.style.transform = 'scale(0.8)';
                setTimeout(() => {
                    item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'scale(1)';
                }, 50);
            } else {
                item.style.display = 'none';
            }
        });
        
        // Update current images for modal navigation
        this.updateCurrentImages();
    }
    
    updateCurrentImages() {
        const visibleItems = document.querySelectorAll('.gallery-item[style*="display: block"], .gallery-item:not([style*="display: none"])');
        this.currentImages = Array.from(visibleItems).map(item => item.querySelector('img')).filter(img => img && img.src);
    }
    
    openModal(img) {
        this.modalImage.src = img.src;
        this.modalImage.alt = img.alt;
        this.modalCaption.textContent = img.dataset.caption;
        this.modalCategory.textContent = img.dataset.category;

        // Handle commentary
        const commentary = img.dataset.commentary;
        if (commentary && commentary.trim()) {
            this.modalCommentary.textContent = commentary;
            this.modalCommentary.style.display = 'block';
        } else {
            this.modalCommentary.style.display = 'none';
        }

        // Find current index
        this.currentIndex = this.currentImages.indexOf(img);

        // Show modal
        this.modal.classList.add('show');
        document.body.style.overflow = 'hidden';

        // Update navigation buttons
        this.updateNavigationButtons();

        // Initialize comments for this image
        this.initializeImageComments(img);
    }
    
    closeModal() {
        this.modal.classList.remove('show');
        document.body.style.overflow = '';
    }
    
    previousImage() {
        if (this.currentImages.length === 0) return;
        
        this.currentIndex = (this.currentIndex - 1 + this.currentImages.length) % this.currentImages.length;
        this.updateModalImage();
    }
    
    nextImage() {
        if (this.currentImages.length === 0) return;
        
        this.currentIndex = (this.currentIndex + 1) % this.currentImages.length;
        this.updateModalImage();
    }
    
    updateModalImage() {
        const img = this.currentImages[this.currentIndex];
        if (img) {
            this.modalImage.src = img.src;
            this.modalImage.alt = img.alt;
            this.modalCaption.textContent = img.dataset.caption;
            this.modalCategory.textContent = img.dataset.category;

            // Handle commentary
            const commentary = img.dataset.commentary;
            if (commentary && commentary.trim()) {
                this.modalCommentary.textContent = commentary;
                this.modalCommentary.style.display = 'block';
            } else {
                this.modalCommentary.style.display = 'none';
            }

            this.updateNavigationButtons();

            // Update comments for the new image
            this.initializeImageComments(img);
        }
    }
    
    updateNavigationButtons() {
        // Hide navigation buttons if there's only one image or no images
        if (this.currentImages.length <= 1) {
            this.modalPrev.style.display = 'none';
            this.modalNext.style.display = 'none';
        } else {
            this.modalPrev.style.display = 'block';
            this.modalNext.style.display = 'block';
        }
    }

    setupCommentSystem() {
        // Set up comment form submission
        if (this.modalCommentForm) {
            this.modalCommentForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleCommentSubmission();
            });
        }

        // Set up character count for comment textarea
        const commentContent = document.getElementById('modal-comment-content');
        const charCount = document.getElementById('modal-char-count');
        if (commentContent && charCount) {
            commentContent.addEventListener('input', () => {
                charCount.textContent = commentContent.value.length;
            });
        }
    }

    initializeImageComments(img) {
        // Create a unique identifier for the image based on its filename
        const imageSlug = this.getImageSlug(img);

        // Update the post slug in the form
        if (this.modalPostSlug) {
            this.modalPostSlug.value = imageSlug;
        }

        // Clean up previous comment system instance
        if (this.currentImageComments) {
            this.currentImageComments.cleanup();
        }

        // Create new comment system instance for this image
        this.currentImageComments = new ImageCommentsSystem(imageSlug, {
            form: this.modalCommentForm,
            commentsList: this.modalCommentsList,
            commentCount: this.modalCommentCount,
            loadingIndicator: this.modalCommentsLoading
        });

        // Initialize the comment system
        this.currentImageComments.init();
    }

    getImageSlug(img) {
        // Extract filename from src and create a slug
        const src = img.src;
        const filename = src.split('/').pop(); // Get filename
        const pathParts = src.split('/');

        // Find the part after 'img/' to get the relative path
        const imgIndex = pathParts.findIndex(part => part === 'img');
        if (imgIndex !== -1 && imgIndex < pathParts.length - 1) {
            const relativePath = pathParts.slice(imgIndex + 1).join('/');
            return 'image-' + relativePath.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
        }

        // Fallback to just filename
        return 'image-' + filename.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
    }

    async handleCommentSubmission() {
        if (this.currentImageComments) {
            await this.currentImageComments.submitComment();
        }
    }
}

/**
 * Image Comments System - extends the main comment system for gallery images
 */
class ImageCommentsSystem {
    constructor(imageSlug, elements) {
        this.imageSlug = imageSlug;
        this.elements = elements;
        this.handlerUrl = 'comments/comment-handler.php';
        this.currentUser = null;
        this.currentPage = 1;
        this.isLoading = false;
    }

    async init() {
        await this.getCurrentUser();
        this.setupForm();
        await this.loadComments();
    }

    async getCurrentUser() {
        try {
            const response = await fetch(this.handlerUrl + '?action=user');
            const result = await response.json();
            if (result.success) {
                this.currentUser = result.user;
                this.updateUserInterface();
            }
        } catch (error) {
            console.error('Error getting current user:', error);
        }
    }

    updateUserInterface() {
        const userInfo = document.getElementById('modal-user-info');
        const userFields = document.getElementById('modal-user-fields');
        const userName = document.getElementById('modal-user-name');
        const userEmail = document.getElementById('modal-user-email');

        if (this.currentUser) {
            if (userName) userName.textContent = this.currentUser.name;
            if (userEmail) userEmail.textContent = `(${this.currentUser.email})`;
            if (userInfo) userInfo.style.display = 'block';
            if (userFields) userFields.style.display = 'none';
        } else {
            if (userInfo) userInfo.style.display = 'none';
            if (userFields) userFields.style.display = 'block';
        }
    }

    setupForm() {
        if (this.elements.form) {
            // Remove any existing listeners
            this.elements.form.removeEventListener('submit', this.boundSubmitHandler);

            // Bind the handler to maintain 'this' context
            this.boundSubmitHandler = (e) => {
                e.preventDefault();
                this.submitComment();
            };

            this.elements.form.addEventListener('submit', this.boundSubmitHandler);
        }
    }

    async loadComments() {
        if (this.isLoading) return;

        this.isLoading = true;
        if (this.elements.loadingIndicator) {
            this.elements.loadingIndicator.style.display = 'block';
        }

        try {
            const response = await fetch(`${this.handlerUrl}?action=comments&post_slug=${encodeURIComponent(this.imageSlug)}&page=${this.currentPage}`);
            const result = await response.json();

            if (result.success) {
                this.renderComments(result.comments);
                this.updateCommentCount(result.total_comments);
            } else {
                console.error('Error loading comments:', result.error);
            }
        } catch (error) {
            console.error('Error loading comments:', error);
        } finally {
            this.isLoading = false;
            if (this.elements.loadingIndicator) {
                this.elements.loadingIndicator.style.display = 'none';
            }
        }
    }

    renderComments(comments) {
        if (!this.elements.commentsList) return;

        this.elements.commentsList.innerHTML = '';

        comments.forEach(comment => {
            const commentElement = this.createCommentElement(comment);
            this.elements.commentsList.appendChild(commentElement);
        });
    }

    createCommentElement(comment) {
        const commentDiv = document.createElement('div');
        commentDiv.className = 'comment';
        commentDiv.innerHTML = `
            <div class="comment-header">
                <div class="comment-avatar">
                    ${this.escapeHtml(comment.name).charAt(0).toUpperCase()}
                </div>
                <div class="comment-meta">
                    <span class="comment-author">${this.escapeHtml(comment.name)}</span>
                    <span class="comment-date">${this.formatDate(comment.created_at)}</span>
                </div>
            </div>
            <div class="comment-content">
                ${this.escapeHtml(comment.content).replace(/\n/g, '<br>')}
            </div>
            <div class="comment-actions">
                ${this.currentUser ? `
                    <button class="vote-btn ${comment.user_vote === 'like' ? 'active' : ''}"
                            data-vote="like" data-comment-id="${comment.id}">
                        👍 <span class="vote-count">${comment.likes || 0}</span>
                    </button>
                    <button class="vote-btn ${comment.user_vote === 'dislike' ? 'active' : ''}"
                            data-vote="dislike" data-comment-id="${comment.id}">
                        👎 <span class="vote-count">${comment.dislikes || 0}</span>
                    </button>
                ` : `
                    <span class="vote-display">👍 ${comment.likes || 0}</span>
                    <span class="vote-display">👎 ${comment.dislikes || 0}</span>
                `}
            </div>
        `;

        // Add vote event listeners
        if (this.currentUser) {
            commentDiv.querySelectorAll('.vote-btn').forEach(btn => {
                btn.addEventListener('click', (e) => this.handleVote(e));
            });
        }

        return commentDiv;
    }

    updateCommentCount(count) {
        if (this.elements.commentCount) {
            this.elements.commentCount.textContent = count;
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }

    async submitComment() {
        // Implementation similar to the main comment system
        // This will be handled by the existing comment handler
        const formData = new FormData(this.elements.form);

        console.log('Submitting comment for image:', this.imageSlug);
        console.log('Form data:', {
            post_slug: this.imageSlug,
            content: formData.get('content'),
            name: formData.get('name'),
            email: formData.get('email')
        });

        try {
            const response = await fetch(this.handlerUrl + '?action=comment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    post_slug: this.imageSlug,
                    content: formData.get('content'),
                    name: formData.get('name'),
                    email: formData.get('email'),
                    parent_id: formData.get('parent_id') || null
                })
            });

            const result = await response.json();
            console.log('Comment submission result:', result);

            if (result.success) {
                this.elements.form.reset();
                const charCount = document.getElementById('modal-char-count');
                if (charCount) charCount.textContent = '0';
                await this.loadComments(); // Reload comments
                alert('Comment posted successfully!');
            } else {
                console.error('Comment submission failed:', result.error);
                alert(result.error || 'Failed to post comment');
            }
        } catch (error) {
            console.error('Error submitting comment:', error);
            alert('Failed to post comment: ' + error.message);
        }
    }

    async handleVote(e) {
        const button = e.target.closest('.vote-btn');
        const commentId = button.dataset.commentId;
        const voteType = button.dataset.vote;

        try {
            const response = await fetch(this.handlerUrl + '?action=vote', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    comment_id: commentId,
                    vote_type: voteType
                })
            });

            const result = await response.json();

            if (result.success) {
                await this.loadComments(); // Reload to update vote counts
            } else {
                alert(result.error || 'Failed to vote');
            }
        } catch (error) {
            console.error('Error voting:', error);
        }
    }

    cleanup() {
        // Clean up any event listeners or resources
        if (this.elements.form && this.boundSubmitHandler) {
            this.elements.form.removeEventListener('submit', this.boundSubmitHandler);
        }
    }
}

// Initialize gallery when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if we're on the gallery page
    if (document.getElementById('gallery-grid')) {
        new Gallery();
    }
});

// Add lazy loading for images and error handling
document.addEventListener('DOMContentLoaded', () => {
    const images = document.querySelectorAll('img[loading="lazy"]');

    // Add error handling for all gallery images
    images.forEach(img => {
        img.addEventListener('error', function() {
            console.warn('Failed to load image:', this.src);
            this.style.display = 'none';
            // Hide the parent gallery item if image fails to load
            const galleryItem = this.closest('.gallery-item');
            if (galleryItem) {
                galleryItem.style.display = 'none';
            }
        });

        img.addEventListener('load', function() {
            console.log('Successfully loaded image:', this.src);
        });
    });

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.src; // Trigger loading
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }
});

// Add masonry layout adjustment on window resize
window.addEventListener('resize', () => {
    // Debounce resize events
    clearTimeout(window.resizeTimeout);
    window.resizeTimeout = setTimeout(() => {
        // Re-trigger any layout calculations if needed
        const galleryGrid = document.getElementById('gallery-grid');
        if (galleryGrid) {
            // Force reflow to adjust masonry layout
            galleryGrid.style.display = 'none';
            galleryGrid.offsetHeight; // Trigger reflow
            galleryGrid.style.display = 'grid';
        }
    }, 250);
});


